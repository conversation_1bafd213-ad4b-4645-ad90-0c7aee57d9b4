{"processed_files": 1, "successful_reads": 1, "failed_reads": 0, "tag_generation_enabled": true, "ctags_available": true, "processing_time_seconds": 0.2219187000000602, "context_folder": "context", "read_results": [{"path": "index.html", "status": "success", "content": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n  <title>Technology Consulting Solutions</title>\n  <link href=\"https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;600;700&family=Inter:wght@400;500;600&display=swap\" rel=\"stylesheet\">\n  <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n  <script src=\"https://cdn.tailwindcss.com\"></script>\n  <style>\n    body, html {\n      font-family: 'Inter', system-ui, sans-serif;\n      background: #111;\n      margin: 0;\n      padding: 0;\n      overflow-x: hidden;\n      min-height: 100vh;\n    }\n    h1, h2, h3, h4 {\n      font-family: 'Manrope', sans-serif !important;\n      letter-spacing: -0.03em !important;\n    }\n    .title-bold {\n      font-weight: 600 !important;\n    }\n    .title-light {\n      font-weight: 200 !important;\n    }\n    #shader-canvas {\n      position: fixed;\n      top: 0; left: 0;\n      width: 100vw;\n      height: 100vh;\n      display: block;\n      z-index: 0;\n      background: #111;\n    }\n    .glass-effect {\n      backdrop-filter: blur(14px) brightness(0.91);\n      -webkit-backdrop-filter: blur(14px) brightness(0.91);\n    }\n    .gradient-text {\n      background-clip: text;\n      -webkit-background-clip: text;\n      color: transparent;\n    }\n    .floating-animation {\n      animation: float 6s ease-in-out infinite;\n    }\n    @keyframes float {\n      0%, 100% { transform: translateY(0px) rotate(0deg); }\n      50% { transform: translateY(-20px) rotate(2deg); }\n    }\n    .pulse-glow {\n      animation: pulse-glow 3s ease-in-out infinite;\n    }\n    @keyframes pulse-glow {\n      0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }\n      50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.6); }\n    }\n    .feature-icon {\n      width: 3rem;\n      height: 3rem;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: rgba(255,255,255,0.08);\n      border: 1px solid rgba(255,255,255,0.15);\n    }\n    .logo-circle {\n      width: 2rem;\n      height: 2rem;\n      border-radius: 50%;\n      border: 2px solid rgba(59, 130, 246, 0.7);\n      position: relative;\n      background: transparent;\n    }\n    .logo-circle::before {\n      content: '';\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      width: 0.5rem;\n      height: 0.5rem;\n      border-radius: 50%;\n      background: rgba(59, 130, 246, 0.9);\n      animation: pulse 2s ease-in-out infinite;\n    }\n    @keyframes pulse {\n      0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }\n      50% { transform: translate(-50%, -50%) scale(1.5); opacity: 0.7; }\n    }\n  </style>\n\n</head>\n<body>\n  <div class=\"fixed inset-0 w-full h-screen\">\n    <iframe src=\"https://my.spline.design/unchained-d3hHCgdWho7a8ATGzKtB11TU/\" frameborder=\"0\" width=\"100%\" height=\"100%\"></iframe>\n  </div>\n  \n  <!-- Navigation -->\n  <nav class=\"relative z-20 glass-effect bg-white/5 border-b border-white/10\">\n    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n      <div class=\"flex justify-between items-center h-16\">\n        <div class=\"flex items-center\">\n          <div class=\"logo-circle mr-3\"></div>\n          <span class=\"text-white font-semibold text-lg\">TechVision Consulting</span>\n        </div>\n        <div class=\"hidden md:flex items-center space-x-8\">\n          <a href=\"#\" class=\"text-white/70 hover:text-white text-sm transition-colors\">Solutions</a>\n          <a href=\"#\" class=\"text-white/70 hover:text-white text-sm transition-colors\">About</a>\n          <a href=\"#\" class=\"text-white/70 hover:text-white text-sm transition-colors\">Case Studies</a>\n          <a href=\"#\" class=\"text-white/70 hover:text-white text-sm transition-colors\">Resources</a>\n          <button class=\"px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white text-sm rounded-lg transition-colors\">\n            Schedule Consultation\n          </button>\n        </div>\n      </div>\n    </div>\n  </nav>\n\n  <!-- Hero Section -->\n  <div class=\"relative z-10 min-h-screen flex items-center justify-center px-4 pt-20\">\n    <div class=\"max-w-7xl mx-auto\">\n      <div class=\"text-center mb-16\">\n        <!-- Main Heading -->\n        <h1 class=\"text-5xl md:text-7xl lg:text-8xl leading-tight tracking-[-0.03em] gradient-text bg-gradient-to-r from-white via-blue-300 to-indigo-400 mb-6\">\n          <span class=\"title-bold\">Technology</span>\n          <br>\n          <span class=\"text-4xl md:text-6xl lg:text-7xl title-bold\">Transformed</span>\n        </h1>\n        \n        <!-- Subtitle -->\n        <p class=\"text-lg md:text-xl text-white/70 max-w-3xl mx-auto mb-8 leading-relaxed\">\n          Is your organization falling behind in the digital race? We help forward-thinking businesses and organizations unlock their potential through strategic technology innovation that delivers measurable ROI and sustainable competitive advantages.\n        </p>\n        \n        <!-- CTA Buttons -->\n        <div class=\"flex flex-col sm:flex-row gap-4 justify-center mb-12\">\n          <button class=\"px-8 py-4 bg-blue-600 hover:bg-blue-500 text-white font-medium rounded-xl transition-all duration-200 pulse-glow\">\n            Get Started Today\n          </button>\n          <button class=\"px-8 py-4 glass-effect bg-white/10 hover:bg-white/15 text-white font-medium rounded-xl transition-all duration-200 border border-white/10\">\n            <i class=\"fas fa-phone mr-2\"></i>\n            Schedule Tech Assessment\n          </button>\n        </div>\n        \n        <!-- Trust Indicators -->\n        <div class=\"flex flex-wrap justify-center gap-6 mb-16\">\n          <span class=\"text-xs text-white/60 px-3 py-2 rounded-full bg-white/5 border border-white/10\">\n            <i class=\"fas fa-link mr-2\"></i>Blockchain Certified\n          </span>\n          <span class=\"text-xs text-white/60 px-3 py-2 rounded-full bg-white/5 border border-white/10\">\n            <i class=\"fas fa-robot mr-2\"></i>AI Specialized\n          </span>\n          <span class=\"text-xs text-white/60 px-3 py-2 rounded-full bg-white/5 border border-white/10\">\n            <i class=\"fas fa-rocket mr-2\"></i>Agile Delivery\n          </span>\n        </div>\n      </div>\n      \n      <!-- Service Cards -->\n      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto\">\n        <!-- Service 1 -->\n        <div class=\"glass-effect bg-gradient-to-br from-white/10 to-white/5 border border-white/10 rounded-2xl p-6 floating-animation\">\n          <div class=\"feature-icon mb-4\">\n            <i class=\"fas fa-link text-blue-400\"></i>\n          </div>\n          <h3 class=\"text-xl text-white mb-3\">Blockchain Architecture</h3>\n          <p class=\"text-white/70 text-sm leading-relaxed\">\n            Design and implement decentralized blockchain solutions that enhance transparency, security, and trust in your business operations.\n          </p>\n          <div class=\"mt-4 flex items-center text-blue-400 text-sm\">\n            <span>Learn more</span>\n            <i class=\"fas fa-arrow-right ml-2\"></i>\n          </div>\n        </div>\n        \n        <!-- Service 2 -->\n        <div class=\"glass-effect bg-gradient-to-br from-white/10 to-white/5 border border-white/10 rounded-2xl p-6 floating-animation\" style=\"animation-delay: -2s;\">\n          <div class=\"feature-icon mb-4\">\n            <i class=\"fas fa-robot text-indigo-400\"></i>\n          </div>\n          <h3 class=\"text-xl text-white mb-3\">AI & Automation</h3>\n          <p class=\"text-white/70 text-sm leading-relaxed\">\n            Leverage artificial intelligence and automation to streamline operations and unlock new business opportunities.\n          </p>\n          <div class=\"mt-4 flex items-center text-indigo-400 text-sm\">\n            <span>Learn more</span>\n            <i class=\"fas fa-arrow-right ml-2\"></i>\n          </div>\n        </div>\n        \n        <!-- Service 3 -->\n        <div class=\"glass-effect bg-gradient-to-br from-white/10 to-white/5 border border-white/10 rounded-2xl p-6 floating-animation\" style=\"animation-delay: -4s;\">\n          <div class=\"feature-icon mb-4\">\n            <i class=\"fas fa-laptop-code text-purple-400\"></i>\n          </div>\n          <h3 class=\"text-xl text-white mb-3\">Technology Solutions</h3>\n          <p class=\"text-white/70 text-sm leading-relaxed\">\n            Comprehensive technology consulting services from system architecture to digital transformation strategies.\n          </p>\n          <div class=\"mt-4 flex items-center text-purple-400 text-sm\">\n            <span>Learn more</span>\n            <i class=\"fas fa-arrow-right ml-2\"></i>\n          </div>\n        </div>\n      </div>\n      \n\n    </div>\n  </div>\n\n  <!-- Services Section -->\n  <div class=\"relative overflow-hidden bg-gradient-to-b from-black via-gray-900/50 to-black\">\n    <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-32 relative z-10\">\n      <div class=\"text-center max-w-4xl mx-auto mb-24\">\n        <div class=\"inline-flex items-center px-4 py-2 rounded-full bg-blue-600/10 border border-blue-500/20 mb-8\">\n          <i class=\"fas fa-rocket text-blue-400 mr-2\"></i>\n          <span class=\"text-blue-300 text-sm font-medium\">Technology Solutions</span>\n        </div>\n        <h2 class=\"text-4xl md:text-6xl lg:text-7xl title-bold gradient-text bg-gradient-to-r from-white via-blue-300 to-indigo-400 mb-8\">\n          Expertise That Drives Innovation\n        </h2>\n        <p class=\"text-lg md:text-xl text-white/70 leading-relaxed\">\n          Ready to outpace your competition? We transform struggling operations into innovation powerhouses. Our proven solutions have helped organizations reduce costs by 40%, accelerate time-to-market, and unlock new revenue streams.\n        </p>\n      </div>\n\n      <!-- Custom Services Grid -->\n      <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16\">\n        <!-- Featured Service 1 -->\n        <div class=\"glass-effect bg-gradient-to-br from-white/10 to-white/5 border border-white/10 rounded-2xl p-8 group hover:border-blue-500/30 transition-all duration-300\">\n          <div class=\"flex items-start space-x-4 mb-6\">\n            <div class=\"feature-icon bg-blue-600/20 border-blue-500/30\">\n              <i class=\"fas fa-link text-blue-400\"></i>\n            </div>\n            <div>\n              <h3 class=\"text-2xl text-white mb-2 title-bold\">Blockchain Architecture</h3>\n              <div class=\"text-blue-400 text-sm font-medium mb-3\">Decentralized Solutions</div>\n            </div>\n          </div>\n          <p class=\"text-white/70 leading-relaxed mb-6\">\n            Eliminate costly intermediaries and streamline operations with blockchain technology. Our clients typically see 30-50% reduction in transaction costs while gaining unbreakable security and transparency that builds customer trust.\n          </p>\n          <div class=\"flex items-center text-blue-400 text-sm group-hover:text-blue-300 transition-colors\">\n            <span>Explore blockchain solutions</span>\n            <i class=\"fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform\"></i>\n          </div>\n        </div>\n          \n        <!-- Featured Service 2 -->\n        <div class=\"glass-effect bg-gradient-to-br from-white/10 to-white/5 border border-white/10 rounded-2xl p-8 group hover:border-purple-500/30 transition-all duration-300\">\n          <div class=\"flex items-start space-x-4 mb-6\">\n            <div class=\"feature-icon bg-purple-600/20 border-purple-500/30\">\n              <i class=\"fas fa-robot text-purple-400\"></i>\n            </div>\n            <div>\n              <h3 class=\"text-2xl text-white mb-2 title-bold\">AI & Automation</h3>\n              <div class=\"text-purple-400 text-sm font-medium mb-3\">Intelligent Systems</div>\n            </div>\n          </div>\n          <p class=\"text-white/70 leading-relaxed mb-6\">\n            Stop losing money to manual processes and human error. Our AI automation solutions have helped organizations save 60+ hours per week, reduce operational costs by 35%, and eliminate 99% of data entry errors. Your competitors are already using AI – don't get left behind.\n          </p>\n          <div class=\"flex items-center text-purple-400 text-sm group-hover:text-purple-300 transition-colors\">\n            <span>Discover AI solutions</span>\n            <i class=\"fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform\"></i>\n          </div>\n        </div>\n      </div>\n\n      <!-- Secondary Services Grid -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16\">\n        <!-- Service Card 1 -->\n        <div class=\"glass-effect bg-gradient-to-br from-white/8 to-white/3 border border-white/10 rounded-xl p-6 group hover:bg-gradient-to-br hover:from-cyan-900/20 hover:to-white/5 hover:border-cyan-500/30 transition-all duration-300\">\n          <div class=\"feature-icon bg-cyan-600/20 border-cyan-500/30 mb-4\">\n            <i class=\"fas fa-cloud text-cyan-400\"></i>\n          </div>\n          <h3 class=\"text-lg text-white mb-2 title-bold\">Cloud Infrastructure</h3>\n          <p class=\"text-white/60 text-sm leading-relaxed mb-4\">\n            Enterprise-grade cloud solutions with 99.9% uptime and scalable architecture.\n          </p>\n          <div class=\"flex items-center text-cyan-400 text-xs group-hover:text-cyan-300 transition-colors\">\n            <span>Learn more</span>\n            <i class=\"fas fa-arrow-right ml-1 text-[10px]\"></i>\n          </div>\n        </div>\n\n        <!-- Service Card 2 -->\n        <div class=\"glass-effect bg-gradient-to-br from-white/8 to-white/3 border border-white/10 rounded-xl p-6 group hover:bg-gradient-to-br hover:from-emerald-900/20 hover:to-white/5 hover:border-emerald-500/30 transition-all duration-300\">\n          <div class=\"feature-icon bg-emerald-600/20 border-emerald-500/30 mb-4\">\n            <i class=\"fas fa-mobile-alt text-emerald-400\"></i>\n          </div>\n          <h3 class=\"text-lg text-white mb-2 title-bold\">Mobile Solutions</h3>\n          <p class=\"text-white/60 text-sm leading-relaxed mb-4\">\n            Cross-platform applications that deliver exceptional user experiences.\n          </p>\n          <div class=\"flex items-center text-emerald-400 text-xs group-hover:text-emerald-300 transition-colors\">\n            <span>Learn more</span>\n            <i class=\"fas fa-arrow-right ml-1 text-[10px]\"></i>\n          </div>\n        </div>\n\n        <!-- Service Card 3 -->\n        <div class=\"glass-effect bg-gradient-to-br from-white/8 to-white/3 border border-white/10 rounded-xl p-6 group hover:bg-gradient-to-br hover:from-amber-900/20 hover:to-white/5 hover:border-amber-500/30 transition-all duration-300\">\n          <div class=\"feature-icon bg-amber-600/20 border-amber-500/30 mb-4\">\n            <i class=\"fas fa-chart-line text-amber-400\"></i>\n          </div>\n          <h3 class=\"text-lg text-white mb-2 title-bold\">Data Analytics</h3>\n          <p class=\"text-white/60 text-sm leading-relaxed mb-4\">\n            Transform data into actionable insights with advanced analytics.\n          </p>\n          <div class=\"flex items-center text-amber-400 text-xs group-hover:text-amber-300 transition-colors\">\n            <span>Learn more</span>\n            <i class=\"fas fa-arrow-right ml-1 text-[10px]\"></i>\n          </div>\n        </div>\n      </div>\n\n      <!-- Additional Services Row -->\n      <div class=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\">\n        <!-- Custom Software Development -->\n        <div class=\"glass-effect bg-gradient-to-br from-white/10 to-white/5 border border-white/10 rounded-2xl p-8 group hover:border-indigo-500/30 transition-all duration-300\">\n          <div class=\"flex items-start space-x-4 mb-6\">\n            <div class=\"feature-icon bg-indigo-600/20 border-indigo-500/30\">\n              <i class=\"fas fa-laptop-code text-indigo-400\"></i>\n            </div>\n            <div>\n              <h3 class=\"text-2xl text-white mb-2 title-bold\">Custom Software Development</h3>\n              <div class=\"text-indigo-400 text-sm font-medium mb-3\">Tailored Solutions</div>\n            </div>\n          </div>\n          <p class=\"text-white/70 leading-relaxed mb-6\">\n            Tired of software that doesn't fit your business? Off-the-shelf solutions cost you time, money, and competitive edge. Our custom software delivers exactly what you need – faster workflows, better user experiences, and systems that scale with your growth.\n          </p>\n          <div class=\"flex items-center text-indigo-400 text-sm group-hover:text-indigo-300 transition-colors\">\n            <span>View our development process</span>\n            <i class=\"fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform\"></i>\n          </div>\n        </div>\n\n        <!-- Cybersecurity Solutions -->\n        <div class=\"glass-effect bg-gradient-to-br from-white/10 to-white/5 border border-white/10 rounded-2xl p-8 group hover:border-red-500/30 transition-all duration-300\">\n          <div class=\"flex items-start space-x-4 mb-6\">\n            <div class=\"feature-icon bg-red-600/20 border-red-500/30\">\n              <i class=\"fas fa-shield-alt text-red-400\"></i>\n            </div>\n            <div>\n              <h3 class=\"text-2xl text-white mb-2 title-bold\">Cybersecurity Solutions</h3>\n              <div class=\"text-red-400 text-sm font-medium mb-3\">Enterprise Protection</div>\n            </div>\n          </div>\n          <p class=\"text-white/70 leading-relaxed mb-6\">\n            One cyberattack can destroy years of hard work and customer trust. Don't gamble with your organization's future. Our cybersecurity solutions have prevented 100% of attempted breaches for our clients, ensuring business continuity and protecting your reputation.\n          </p>\n          <div class=\"flex items-center text-red-400 text-sm group-hover:text-red-300 transition-colors\">\n            <span>Assess your security posture</span>\n            <i class=\"fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform\"></i>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"h-px bg-gradient-to-r from-transparent via-gray-800 to-transparent my-16\"></div>\n\n      <!-- Why Choose Us Section -->\n      <div class=\"text-center max-w-4xl mx-auto mb-20\">\n        <div class=\"inline-flex items-center px-4 py-2 rounded-full bg-green-600/10 border border-green-500/20 mb-8\">\n          <i class=\"fas fa-trophy text-green-400 mr-2\"></i>\n          <span class=\"text-green-300 text-sm font-medium\">Proven Results</span>\n        </div>\n        <h2 class=\"text-4xl md:text-6xl lg:text-7xl title-bold gradient-text bg-gradient-to-r from-white via-blue-300 to-indigo-400 mb-8\">\n          Why Choose TechVision\n        </h2>\n        <p class=\"text-lg md:text-xl text-white/70 leading-relaxed mb-16\">\n          Fresh perspective, cutting-edge expertise, and a commitment to your success. Here's what sets us apart as your technology transformation partner.\n        </p>\n      </div>\n\n      <!-- Stacking Cards Container -->\n      <div class=\"stacking-cards-container relative h-[600px] overflow-hidden mb-16\">\n        <!-- Card 1 - Modern Approach -->\n        <div class=\"advantage-card card-1 absolute w-full h-32 glass-effect bg-gradient-to-br from-blue-900/20 to-black border border-blue-500/30 rounded-2xl\">\n          <div class=\"flex items-center h-full px-8\">\n            <div class=\"feature-icon bg-blue-600/20 border-blue-500/30 mr-6\">\n              <i class=\"fas fa-clock text-blue-400\"></i>\n            </div>\n            <div class=\"flex-1\">\n              <h3 class=\"text-2xl text-white title-bold mb-2\">Modern Approach</h3>\n              <p class=\"text-white/70 text-sm\">Latest tech stack & methodologies</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- Card 2 - Results-Focused -->\n        <div class=\"advantage-card card-2 absolute w-full h-32 glass-effect bg-gradient-to-br from-green-900/20 to-black border border-green-500/30 rounded-2xl\">\n          <div class=\"flex items-center h-full px-8\">\n            <div class=\"feature-icon bg-green-600/20 border-green-500/30 mr-6\">\n              <i class=\"fas fa-chart-line text-green-400\"></i>\n            </div>\n            <div class=\"flex-1\">\n              <h3 class=\"text-2xl text-white title-bold mb-2\">Results-Focused</h3>\n              <p class=\"text-white/70 text-sm\">Business value-driven approach</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- Card 3 - Deep Expertise -->\n        <div class=\"advantage-card card-3 absolute w-full h-32 glass-effect bg-gradient-to-br from-purple-900/20 to-black border border-purple-500/30 rounded-2xl\">\n          <div class=\"flex items-center h-full px-8\">\n            <div class=\"feature-icon bg-purple-600/20 border-purple-500/30 mr-6\">\n              <i class=\"fas fa-users text-purple-400\"></i>\n            </div>\n            <div class=\"flex-1\">\n              <h3 class=\"text-2xl text-white title-bold mb-2\">Deep Expertise</h3>\n              <p class=\"text-white/70 text-sm\">Senior-level expertise from day one</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- Card 4 - Transparent Partnership -->\n        <div class=\"advantage-card card-4 absolute w-full h-32 glass-effect bg-gradient-to-br from-orange-900/20 to-black border border-orange-500/30 rounded-2xl\">\n          <div class=\"flex items-center h-full px-8\">\n            <div class=\"feature-icon bg-orange-600/20 border-orange-500/30 mr-6\">\n              <i class=\"fas fa-shield-alt text-orange-400\"></i>\n            </div>\n            <div class=\"flex-1\">\n              <h3 class=\"text-2xl text-white title-bold mb-2\">Transparent Partnership</h3>\n              <p class=\"text-white/70 text-sm\">Complete transparency & accountability</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- Card 5 - Dedicated Support -->\n        <div class=\"advantage-card card-5 absolute w-full h-32 glass-effect bg-gradient-to-br from-red-900/20 to-black border border-red-500/30 rounded-2xl\">\n          <div class=\"flex items-center h-full px-8\">\n            <div class=\"feature-icon bg-red-600/20 border-red-500/30 mr-6\">\n              <i class=\"fas fa-headset text-red-400\"></i>\n            </div>\n            <div class=\"flex-1\">\n              <h3 class=\"text-2xl text-white title-bold mb-2\">Dedicated Support</h3>\n              <p class=\"text-white/70 text-sm\">Direct access to senior consultants</p>\n            </div>\n          </div>\n        </div>\n\n        <!-- Card 6 - Scalable Solutions -->\n        <div class=\"advantage-card card-6 absolute w-full h-32 glass-effect bg-gradient-to-br from-cyan-900/20 to-black border border-cyan-500/30 rounded-2xl\">\n          <div class=\"flex items-center h-full px-8\">\n            <div class=\"feature-icon bg-cyan-600/20 border-cyan-500/30 mr-6\">\n              <i class=\"fas fa-handshake text-cyan-400\"></i>\n            </div>\n            <div class=\"flex-1\">\n              <h3 class=\"text-2xl text-white title-bold mb-2\">Scalable Solutions</h3>\n              <p class=\"text-white/70 text-sm\">Future-proof architecture & design</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Scroll Spacer for Animation -->\n      <div class=\"h-[40vh]\"></div>\n\n      <!-- Success Stats -->\n      <div class=\"glass-effect bg-gradient-to-br from-white/5 to-white/2 border border-white/10 rounded-2xl p-8 md:p-12 mb-16\">\n        <div class=\"text-center mb-8\">\n          <h3 class=\"text-2xl md:text-3xl title-bold text-white mb-4\">\n            Our Commitment to Excellence\n          </h3>\n          <p class=\"text-white/70\">\n            Ready to be your trusted technology partner from day one – bringing fresh ideas and proven expertise to every project.\n          </p>\n        </div>\n        \n        <div class=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n          <div class=\"text-center\">\n            <div class=\"text-3xl md:text-4xl font-[200] text-white mb-2\">100%</div>\n            <div class=\"text-sm text-white/60\">Focused Attention</div>\n          </div>\n          <div class=\"text-center\">\n            <div class=\"text-3xl md:text-4xl font-[200] text-white mb-2\">24/7</div>\n            <div class=\"text-sm text-white/60\">Availability</div>\n          </div>\n          <div class=\"text-center\">\n            <div class=\"text-3xl md:text-4xl font-[200] text-white mb-2\">0%</div>\n            <div class=\"text-sm text-white/60\">Hidden Fees</div>\n          </div>\n          <div class=\"text-center\">\n            <div class=\"text-3xl md:text-4xl font-[200] text-white mb-2\">∞</div>\n            <div class=\"text-sm text-white/60\">Growth Potential</div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"h-px bg-gradient-to-r from-transparent via-gray-800 to-transparent my-16\"></div>\n\n      <!-- Contact Section -->\n      <div class=\"max-w-6xl mx-auto\">\n        <div class=\"text-center mb-16\">\n          <div class=\"inline-flex items-center px-4 py-2 rounded-full bg-blue-600/10 border border-blue-500/20 mb-8\">\n            <i class=\"fas fa-paper-plane text-blue-400 mr-2\"></i>\n            <span class=\"text-blue-300 text-sm font-medium\">Get In Touch</span>\n          </div>\n          <h2 class=\"text-4xl md:text-6xl lg:text-7xl title-bold gradient-text bg-gradient-to-r from-white via-blue-300 to-indigo-400 mb-8\">\n            Start Your Transformation\n          </h2>\n          <p class=\"text-lg md:text-xl text-white/70 leading-relaxed max-w-3xl mx-auto\">\n            Ready to revolutionize your technology infrastructure? Let's discuss how TechVision can accelerate your digital transformation and deliver measurable results.\n          </p>\n        </div>\n\n        <div class=\"glass-effect bg-gradient-to-br from-white/8 to-white/3 border border-white/10 rounded-3xl overflow-hidden\">\n          <div class=\"grid grid-cols-1 lg:grid-cols-2\">\n            <!-- Left side - Contact Info -->\n            <div class=\"p-8 lg:p-12 bg-gradient-to-br from-blue-900/30 to-purple-900/20 relative overflow-hidden\">\n              <!-- Background Pattern -->\n              <div class=\"absolute inset-0 opacity-5\">\n                <div class=\"absolute top-4 left-4 w-32 h-32 border border-white/20 rounded-full\"></div>\n                <div class=\"absolute bottom-8 right-8 w-24 h-24 border border-white/20 rounded-full\"></div>\n                <div class=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 border border-white/10 rounded-full\"></div>\n              </div>\n              \n              <div class=\"relative z-10\">\n                <h3 class=\"text-2xl md:text-3xl title-bold text-white mb-6\">Let's Build Something Amazing</h3>\n                <p class=\"text-white/70 text-lg leading-relaxed mb-8\">\n                  Whether you need blockchain architecture, AI automation, or custom software development, we're here to turn your vision into reality.\n                </p>\n\n                <!-- Contact Methods -->\n                <div class=\"space-y-6 mb-8\">\n                  <div class=\"flex items-start space-x-4\">\n                    <div class=\"feature-icon bg-blue-600/20 border-blue-500/30 mt-1\">\n                      <i class=\"fas fa-envelope text-blue-400\"></i>\n                    </div>\n                    <div>\n                      <p class=\"text-white font-medium mb-1\">Email Us</p>\n                      <p class=\"text-white/70\"><EMAIL></p>\n                    </div>\n                  </div>\n                  \n                  <div class=\"flex items-start space-x-4\">\n                    <div class=\"feature-icon bg-green-600/20 border-green-500/30 mt-1\">\n                      <i class=\"fas fa-phone text-green-400\"></i>\n                    </div>\n                    <div>\n                      <p class=\"text-white font-medium mb-1\">Schedule a Call</p>\n                      <p class=\"text-white/70\">+1 (555) 123-TECH</p>\n                    </div>\n                  </div>\n                  \n                  <div class=\"flex items-start space-x-4\">\n                    <div class=\"feature-icon bg-purple-600/20 border-purple-500/30 mt-1\">\n                      <i class=\"fas fa-map-marker-alt text-purple-400\"></i>\n                    </div>\n                    <div>\n                      <p class=\"text-white font-medium mb-1\">Location</p>\n                      <p class=\"text-white/70\">Global Remote Team</p>\n                    </div>\n                  </div>\n                </div>\n\n                <!-- Social Links -->\n                <div class=\"space-y-4\">\n                  <p class=\"text-white/70 text-sm\">Follow our journey</p>\n                  <div class=\"flex space-x-4\">\n                    <a href=\"#\" class=\"w-10 h-10 glass-effect bg-white/10 border border-white/20 rounded-xl flex items-center justify-center text-white/70 hover:text-white hover:border-blue-500/50 transition-all duration-200\">\n                      <i class=\"fab fa-linkedin-in\"></i>\n                    </a>\n                    <a href=\"#\" class=\"w-10 h-10 glass-effect bg-white/10 border border-white/20 rounded-xl flex items-center justify-center text-white/70 hover:text-white hover:border-blue-500/50 transition-all duration-200\">\n                      <i class=\"fab fa-twitter\"></i>\n                    </a>\n                    <a href=\"#\" class=\"w-10 h-10 glass-effect bg-white/10 border border-white/20 rounded-xl flex items-center justify-center text-white/70 hover:text-white hover:border-blue-500/50 transition-all duration-200\">\n                      <i class=\"fab fa-github\"></i>\n                    </a>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Right side - Contact Form -->\n            <div class=\"p-8 lg:p-12 border-t lg:border-t-0 lg:border-l border-white/10\">\n              <form class=\"space-y-6\">\n                <!-- Name & Email Row -->\n                <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label for=\"contact-name\" class=\"block text-sm font-medium text-white/80 mb-2\">Name *</label>\n                    <input \n                      type=\"text\" \n                      id=\"contact-name\" \n                      required\n                      class=\"w-full px-4 py-3 glass-effect bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200\"\n                      placeholder=\"Your name\"\n                    >\n                  </div>\n                  <div>\n                    <label for=\"contact-email\" class=\"block text-sm font-medium text-white/80 mb-2\">Email *</label>\n                    <input \n                      type=\"email\" \n                      id=\"contact-email\" \n                      required\n                      class=\"w-full px-4 py-3 glass-effect bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200\"\n                      placeholder=\"<EMAIL>\"\n                    >\n                  </div>\n                </div>\n\n                <!-- Company & Project Type -->\n                <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <label for=\"contact-company\" class=\"block text-sm font-medium text-white/80 mb-2\">Company</label>\n                    <input \n                      type=\"text\" \n                      id=\"contact-company\"\n                      class=\"w-full px-4 py-3 glass-effect bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200\"\n                      placeholder=\"Your company\"\n                    >\n                  </div>\n                  <div>\n                    <label for=\"contact-service\" class=\"block text-sm font-medium text-white/80 mb-2\">Service Interest</label>\n                    <select \n                      id=\"contact-service\"\n                      class=\"w-full px-4 py-3 glass-effect bg-white/5 border border-white/20 rounded-xl text-white focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200 appearance-none cursor-pointer\"\n                    >\n                      <option value=\"\" class=\"bg-gray-900\">Select a service</option>\n                      <option value=\"blockchain\" class=\"bg-gray-900\">Blockchain Architecture</option>\n                      <option value=\"ai\" class=\"bg-gray-900\">AI & Automation</option>\n                      <option value=\"custom-software\" class=\"bg-gray-900\">Custom Software Development</option>\n                      <option value=\"cybersecurity\" class=\"bg-gray-900\">Cybersecurity Solutions</option>\n                      <option value=\"cloud\" class=\"bg-gray-900\">Cloud Infrastructure</option>\n                      <option value=\"consultation\" class=\"bg-gray-900\">Technology Consultation</option>\n                    </select>\n                  </div>\n                </div>\n\n                <!-- Budget Range -->\n                <div>\n                  <label for=\"contact-budget\" class=\"block text-sm font-medium text-white/80 mb-2\">Project Budget</label>\n                  <select \n                    id=\"contact-budget\"\n                    class=\"w-full px-4 py-3 glass-effect bg-white/5 border border-white/20 rounded-xl text-white focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200 appearance-none cursor-pointer\"\n                  >\n                    <option value=\"\" class=\"bg-gray-900\">Select budget range</option>\n                    <option value=\"10k-25k\" class=\"bg-gray-900\">$10k - $25k</option>\n                    <option value=\"25k-50k\" class=\"bg-gray-900\">$25k - $50k</option>\n                    <option value=\"50k-100k\" class=\"bg-gray-900\">$50k - $100k</option>\n                    <option value=\"100k+\" class=\"bg-gray-900\">$100k+</option>\n                    <option value=\"discuss\" class=\"bg-gray-900\">Let's discuss</option>\n                  </select>\n                </div>\n\n                <!-- Message -->\n                <div>\n                  <label for=\"contact-message\" class=\"block text-sm font-medium text-white/80 mb-2\">Project Details *</label>\n                  <textarea \n                    id=\"contact-message\" \n                    rows=\"4\" \n                    required\n                    class=\"w-full px-4 py-3 glass-effect bg-white/5 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:border-blue-500/50 focus:bg-white/10 transition-all duration-200 resize-none\"\n                    placeholder=\"Tell us about your project, challenges, and goals. What technology transformation are you looking to achieve?\"\n                  ></textarea>\n                </div>\n\n                <!-- Submit Button -->\n                <div class=\"pt-4\">\n                  <button \n                    type=\"submit\" \n                    class=\"w-full px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white font-medium rounded-xl transition-all duration-200 pulse-glow flex items-center justify-center space-x-2\"\n                  >\n                    <span>Send Message</span>\n                    <i class=\"fas fa-paper-plane ml-2\"></i>\n                  </button>\n                  <p class=\"text-white/50 text-xs text-center mt-3\">\n                    We'll respond within 24 hours with next steps\n                  </p>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"h-px bg-gradient-to-r from-transparent via-gray-800 to-transparent my-20\"></div>\n\n      <!-- Final CTA Section -->\n      <div class=\"text-center max-w-4xl mx-auto\">\n        <h2 class=\"text-3xl md:text-4xl title-bold gradient-text bg-gradient-to-r from-white via-blue-300 to-indigo-400 mb-6\">\n          Ready to Transform Your Technology?\n        </h2>\n        <p class=\"text-lg text-white/80 max-w-2xl mx-auto mb-8 leading-relaxed\">\n          Every day you wait, your competition gets further ahead. Take action now. Schedule a free consultation and discover how we can transform your organization's technology challenges into competitive advantages.\n        </p>\n        <div class=\"flex flex-col sm:flex-row gap-4 justify-center\">\n          <button class=\"px-8 py-4 bg-blue-600 hover:bg-blue-500 text-white font-medium rounded-xl transition-all duration-200 pulse-glow\">\n            Get FREE Strategy Session\n          </button>\n          <button class=\"px-8 py-4 glass-effect bg-white/10 hover:bg-white/15 text-white font-medium rounded-xl transition-all duration-200 border border-white/10\">\n            <i class=\"fas fa-folder-open mr-2\"></i>\n            View Case Studies\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Footer Section -->\n  <footer class=\"pt-16 pb-8 px-6 md:px-12 bg-zinc-900/80 border-t border-zinc-800 backdrop-blur-xl\">\n    <div class=\"max-w-7xl mx-auto flex flex-col md:flex-row md:items-center md:justify-between mb-10\">\n      <div class=\"mb-8 md:mb-0\">\n        <div class=\"text-2xl font-semibold tracking-tight mb-2\">\n          TechVision<span class=\"text-blue-500\">.</span>\n        </div>\n        <p class=\"text-gray-400 text-[15px] max-w-sm\">\n          Transforming businesses through innovative technology solutions. Blockchain, AI, and custom software that drives results.\n        </p>\n      </div>\n      <nav class=\"flex flex-wrap gap-x-8 gap-y-2 items-center text-[15px]\">\n        <a href=\"#\" class=\"hover:text-blue-400 transition-colors\">Home</a>\n        <a href=\"#services\" class=\"hover:text-blue-400 transition-colors\">Services</a>\n        <a href=\"#why-choose-us\" class=\"hover:text-blue-400 transition-colors\">Why Choose Us</a>\n        <a href=\"#contact\" class=\"hover:text-blue-400 transition-colors\">Contact</a>\n        <a href=\"#\" class=\"hover:text-blue-400 transition-colors\">Blog</a>\n      </nav>\n    </div>\n    <div class=\"border-t border-zinc-800 pt-8 flex flex-col md:flex-row md:items-center md:justify-between\">\n      <div class=\"text-gray-500 text-[14px] mb-4 md:mb-0\">\n        &copy; 2024 TechVision Consulting. All rights reserved.\n      </div>\n      <div class=\"flex space-x-4\">\n        <a href=\"#\" class=\"text-gray-400 hover:text-blue-500 transition-colors\" aria-label=\"LinkedIn\">\n          <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n          </svg>\n        </a>\n        <a href=\"#\" class=\"text-gray-400 hover:text-blue-500 transition-colors\" aria-label=\"Twitter\">\n          <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n          </svg>\n        </a>\n        <a href=\"#\" class=\"text-gray-400 hover:text-blue-500 transition-colors\" aria-label=\"GitHub\">\n          <svg class=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n          </svg>\n        </a>\n      </div>\n    </div>\n  </footer>\n\n  <script src=\"https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js\"></script>\n  <script src=\"https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js\"></script>\n  \n  <script>\n    // Register GSAP ScrollTrigger\n    gsap.registerPlugin(ScrollTrigger);\n\n    // Stacking Cards Animation\n    const cards = gsap.utils.toArray(\".advantage-card\");\n    const cardHeight = 128; // h-32 = 128px\n    const visibleOffset = 25; // Visible offset when stacked\n    const animationDistance = 400; // Fixed scroll distance\n\n    // Set initial positions - cards start fully stacked\n    cards.forEach((card, index) => {\n      gsap.set(card, { \n        y: index * visibleOffset,\n        opacity: 1,\n        zIndex: cards.length - index // Proper stacking order\n      });\n    });\n\n    // Create smooth stacking animation without pinning\n    ScrollTrigger.create({\n      trigger: \".stacking-cards-container\",\n      start: \"top 80%\",\n      end: \"bottom 20%\",\n      scrub: 0.5, // Smooth but responsive\n      animation: gsap.timeline()\n        .to(cards.slice(1), {\n          y: (index, element) => {\n            const cardIndex = cards.indexOf(element);\n            return cardIndex * cardHeight; // Spread out to full height\n          },\n          duration: 1,\n          ease: \"power1.inOut\" // Slight easing for smoothness\n        })\n    });\n\n    // Animate card content on reveal\n    cards.forEach((card, index) => {\n      const title = card.querySelector('h3');\n      const description = card.querySelector('p');\n      const icon = card.querySelector('i');\n\n      // Set initial state\n      gsap.set([title, description, icon], { opacity: 0, x: -30 });\n\n      ScrollTrigger.create({\n        trigger: card,\n        start: \"top 80%\",\n        end: \"top 20%\",\n        onEnter: () => {\n          gsap.to([icon, title, description], {\n            opacity: 1,\n            x: 0,\n            duration: 0.8,\n            stagger: 0.1,\n            ease: \"power2.out\"\n          });\n        }\n      });\n    });\n  </script>\n</body>\n</html> ", "output_file": "context\\index.content.json", "lines": 816, "size_bytes": 42908}], "tag_results": [{"path": "index.html", "status": "success", "output_file": "context\\index.tags.json", "tags_count": 30}]}