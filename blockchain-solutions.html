<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Blockchain Architecture & Crypto Solutions | Disrupt the Block</title>
  <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@200;300;400;600;700&family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/vanta@latest/dist/vanta.waves.min.js"></script>
  <style>
    body, html {
      font-family: 'Inter', system-ui, sans-serif;
      background: #111;
      margin: 0;
      padding: 0;
      overflow-x: hidden;
      min-height: 100vh;
    }
    h1, h2, h3, h4 {
      font-family: 'Manrope', sans-serif !important;
      letter-spacing: -0.03em !important;
    }
    .title-bold {
      font-weight: 600 !important;
    }
    .title-light {
      font-weight: 200 !important;
    }
    .glass-effect {
      backdrop-filter: blur(14px) brightness(0.91);
      -webkit-backdrop-filter: blur(14px) brightness(0.91);
    }
    .gradient-text {
      background-clip: text;
      -webkit-background-clip: text;
      color: transparent;
      /* Fallback color for unsupported browsers */
      -webkit-text-fill-color: transparent;
      /* Prevent text cutoff */
      line-height: 1.1;
      padding: 0.1em 0;
      /* Ensure proper rendering */
      display: inline-block;
      position: relative;
    }

    /* Fallback for browsers that don't support background-clip */
    @supports not (background-clip: text) {
      .gradient-text {
        color: #ffffff;
        background: none;
      }
    }
    .floating-animation {
      animation: float 6s ease-in-out infinite;
    }
    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(2deg); }
    }
    .pulse-glow {
      animation: pulse-glow 3s ease-in-out infinite;
    }
    @keyframes pulse-glow {
      0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
      50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.6); }
    }
    .feature-icon {
      width: 3rem;
      height: 3rem;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255,255,255,0.08);
      border: 1px solid rgba(255,255,255,0.15);
    }
    .logo-circle {
      width: 2rem;
      height: 2rem;
      border-radius: 50%;
      border: 2px solid rgba(59, 130, 246, 0.7);
      position: relative;
      background: transparent;
    }
    .logo-circle::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 0.5rem;
      height: 0.5rem;
      border-radius: 50%;
      background: rgba(59, 130, 246, 0.9);
      animation: pulse 2s ease-in-out infinite;
    }
    @keyframes pulse {
      0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
      50% { transform: translate(-50%, -50%) scale(1.5); opacity: 0.7; }
    }
    .hero-text {
      background: linear-gradient(to right, #ffffff 0%, #f59e0b 45%, #f97316 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    .crypto-particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: rgba(245, 158, 11, 0.6);
      border-radius: 50%;
      animation: cryptoFloat 8s ease-in-out infinite;
    }
    @keyframes cryptoFloat {
      0%, 100% { transform: translateY(0px) translateX(0px) scale(1); opacity: 0.6; }
      25% { transform: translateY(-30px) translateX(20px) scale(1.2); opacity: 1; }
      50% { transform: translateY(-60px) translateX(-10px) scale(0.8); opacity: 0.8; }
      75% { transform: translateY(-20px) translateX(-30px) scale(1.1); opacity: 0.9; }
    }
    .blockchain-hex {
      position: absolute;
      width: 6px;
      height: 6px;
      background: rgba(249, 115, 22, 0.4);
      clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
      animation: hexFloat 10s ease-in-out infinite;
    }
    @keyframes hexFloat {
      0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); opacity: 0.4; }
      33% { transform: translateY(-40px) translateX(30px) rotate(120deg); opacity: 1; }
      66% { transform: translateY(-20px) translateX(-20px) rotate(240deg); opacity: 0.7; }
    }
  </style>
</head>
<body>
  <!-- Navigation -->
  <nav class="relative z-20 glass-effect bg-white/5 border-b border-white/10">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <div class="logo-circle mr-3"></div>
          <span class="text-white font-semibold text-lg">Disrupt the Block</span>
        </div>
        <div class="hidden md:flex items-center space-x-8">
          <a href="index.html" class="text-white/70 hover:text-white text-sm transition-colors">Home</a>
          <a href="#" class="text-white/70 hover:text-white text-sm transition-colors">Solutions</a>
          <a href="#" class="text-white/70 hover:text-white text-sm transition-colors">About</a>
          <a href="#" class="text-white/70 hover:text-white text-sm transition-colors">Case Studies</a>
          <a href="#" class="text-white/70 hover:text-white text-sm transition-colors">Resources</a>
          <button class="px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white text-sm rounded-lg transition-colors">
            Schedule Consultation
          </button>
        </div>
        <!-- Mobile Menu Button -->
        <button id="mobile-menu-button" class="md:hidden text-white focus:outline-none">
          <i class="fas fa-bars text-xl"></i>
        </button>
      </div>
    </div>
    
    <!-- Mobile Navigation Links -->
    <div id="mobile-menu" class="md:hidden hidden absolute top-16 inset-x-0 z-30 glass-effect bg-zinc-900/80 border-t border-white/10 px-6 py-8">
      <div class="grid grid-cols-2 gap-4">
        <!-- Home -->
        <a href="index.html" class="flex flex-col items-center justify-center p-4 glass-effect bg-white/5 border border-white/10 rounded-xl hover:border-blue-500/50 transition-all">
          <i class="fas fa-home text-blue-400 text-2xl mb-2"></i>
          <span class="text-sm text-white/80">Home</span>
        </a>
        <!-- Solutions -->
        <a href="#" class="flex flex-col items-center justify-center p-4 glass-effect bg-white/5 border border-white/10 rounded-xl hover:border-blue-500/50 transition-all">
          <i class="fas fa-cubes text-blue-400 text-2xl mb-2"></i>
          <span class="text-sm text-white/80">Solutions</span>
        </a>
        <!-- About -->
        <a href="#" class="flex flex-col items-center justify-center p-4 glass-effect bg-white/5 border border-white/10 rounded-xl hover:border-blue-500/50 transition-all">
          <i class="fas fa-user-astronaut text-purple-400 text-2xl mb-2"></i>
          <span class="text-sm text-white/80">About</span>
        </a>
        <!-- Case Studies -->
        <a href="#" class="flex flex-col items-center justify-center p-4 glass-effect bg-white/5 border border-white/10 rounded-xl hover:border-blue-500/50 transition-all">
          <i class="fas fa-briefcase text-amber-400 text-2xl mb-2"></i>
          <span class="text-sm text-white/80">Case Studies</span>
        </a>
        <!-- Resources -->
        <a href="#" class="flex flex-col items-center justify-center p-4 glass-effect bg-white/5 border border-white/10 rounded-xl hover:border-blue-500/50 transition-all">
          <i class="fas fa-book-open text-emerald-400 text-2xl mb-2"></i>
          <span class="text-sm text-white/80">Resources</span>
        </a>
        <!-- Contact -->
        <a href="#contact" class="flex flex-col items-center justify-center p-4 glass-effect bg-white/5 border border-white/10 rounded-xl hover:border-blue-500/50 transition-all">
          <i class="fas fa-envelope text-cyan-400 text-2xl mb-2"></i>
          <span class="text-sm text-white/80">Contact</span>
        </a>
        <!-- Consultation CTA spans two columns -->
        <button class="col-span-2 px-4 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-500 hover:to-indigo-500 text-white rounded-xl font-medium transition-all">
          Schedule Consultation
        </button>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section id="hero" class="relative h-screen flex items-center pt-16">
    <div id="vanta-bg" class="absolute inset-0 z-0"></div>
    
    <!-- Crypto Particles -->
    <div class="crypto-particle" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
    <div class="crypto-particle" style="top: 40%; right: 15%; animation-delay: 2s;"></div>
    <div class="crypto-particle" style="bottom: 30%; left: 20%; animation-delay: 4s;"></div>
    <div class="crypto-particle" style="top: 60%; right: 25%; animation-delay: 6s;"></div>
    
    <!-- Blockchain Hexagons -->
    <div class="blockchain-hex" style="top: 15%; left: 15%; animation-delay: 1s;"></div>
    <div class="blockchain-hex" style="top: 35%; right: 20%; animation-delay: 3s;"></div>
    <div class="blockchain-hex" style="bottom: 25%; left: 25%; animation-delay: 5s;"></div>
    <div class="blockchain-hex" style="top: 55%; right: 30%; animation-delay: 7s;"></div>
    
    <div class="container mx-auto px-6 relative z-10">
      <div class="max-w-6xl mx-auto text-center">
        <div class="inline-flex items-center px-4 py-2 rounded-full bg-orange-600/10 border border-orange-500/20 mb-8">
          <i class="fas fa-link text-orange-400 mr-2"></i>
          <span class="text-orange-300 text-sm font-medium">Blockchain Architecture & Crypto Solutions</span>
        </div>
        
        <h1 class="hero-text text-6xl sm:text-7xl md:text-8xl lg:text-9xl font-black tracking-tight mb-6 leading-[0.9] px-4 title-bold">
          BLOCKCHAIN
          <br>
          <span class="text-5xl sm:text-6xl md:text-7xl lg:text-8xl">REVOLUTION</span>
        </h1>
        
        <p class="text-gray-300 text-lg md:text-xl max-w-3xl mx-auto leading-relaxed mt-8 px-4">
          Build the future with secure, scalable blockchain solutions. From DeFi protocols to NFT platforms, we architect decentralized systems that drive innovation and unlock new revenue streams worth millions.
        </p>
        
        <div class="mt-10 flex flex-col sm:flex-row justify-center gap-4 px-4">
          <button class="px-8 py-4 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-500 hover:to-red-500 text-white font-medium rounded-xl transition-all duration-200 pulse-glow">
            Launch Your Blockchain
          </button>
          <button class="px-8 py-4 glass-effect bg-white/10 hover:bg-white/15 text-white font-medium rounded-xl transition-all duration-200 border border-orange-500/20">
            <i class="fas fa-play mr-2"></i>
            Watch Demo
          </button>
        </div>
        
        <!-- Trust Stats -->
        <div class="flex flex-wrap justify-center gap-8 mt-12">
          <div class="text-center">
            <div class="text-3xl font-bold text-white mb-1">$50M+</div>
            <div class="text-sm text-white/60">TVL Secured</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-white mb-1">99.9%</div>
            <div class="text-sm text-white/60">Uptime</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-white mb-1">500K+</div>
            <div class="text-sm text-white/60">Transactions</div>
          </div>
        </div>
      </div>
    </div>
    
    <button id="scroll-arrow" class="absolute bottom-10 left-1/2 transform -translate-x-1/2 animate-bounce cursor-pointer hover:text-white/80 transition-colors focus:outline-none">
      <i class="fas fa-chevron-down text-white/60 text-xl"></i>
    </button>
  </section>

  <!-- Blockchain Solutions Section -->
  <section id="blockchain-solutions" class="relative py-20 md:py-32 bg-gradient-to-b from-black via-gray-900/50 to-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center max-w-4xl mx-auto mb-20">
        <h2 class="text-4xl md:text-6xl lg:text-7xl leading-[1.1] title-bold gradient-text bg-gradient-to-r from-white via-orange-300 to-red-400 mb-8">
          Blockchain Solutions That Scale
        </h2>
        <p class="text-lg md:text-xl text-white/70 leading-relaxed">
          Don't miss the blockchain revolution. Our enterprise-grade solutions deliver security, scalability, and innovation that positions your business at the forefront of Web3 technology.
        </p>
      </div>

      <!-- Solutions Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
        <!-- DeFi Protocol Development -->
        <div class="glass-effect bg-gradient-to-br from-white/10 to-white/5 border border-white/10 rounded-2xl p-8 group hover:border-orange-500/30 transition-all duration-300 floating-animation">
          <div class="flex items-start space-x-4 mb-6">
            <div class="feature-icon bg-orange-600/20 border-orange-500/30">
              <i class="fas fa-coins text-orange-400"></i>
            </div>
            <div>
              <h3 class="text-2xl text-white mb-2 title-bold">dApp Development</h3>
              <div class="text-orange-400 text-sm font-medium mb-3">Scalable, User-Centric Web3 Applications</div>
            </div>
          </div>
          <p class="text-white/70 leading-relaxed mb-6">
            We design and build decentralized applications that are fast, secure, and easy to use. From wallet integration to on-chain interactions, we create seamless user experiences backed by robust Web3 infrastructure.
          </p>
          <div class="flex items-center text-orange-400 text-sm group-hover:text-orange-300 transition-colors">
            <span>Explore dApp solutions</span>
            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
          </div>
        </div>

        <!-- Web3 Advisory & Product Validation -->
        <div class="glass-effect bg-gradient-to-br from-white/10 to-white/5 border border-white/10 rounded-2xl p-8 group hover:border-blue-500/30 transition-all duration-300 floating-animation" style="animation-delay: -2s;">
          <div class="flex items-start space-x-4 mb-6">
            <div class="feature-icon bg-blue-600/20 border-blue-500/30">
              <i class="fas fa-lightbulb text-blue-400"></i>
            </div>
            <div>
              <h3 class="text-2xl text-white mb-2 title-bold"> Web3 Advisory & Product Validation</h3>
              <div class="text-blue-400 text-sm font-medium mb-3">Make Smart Decisions in a Noisy Market</div>
            </div>
          </div>
          <p class="text-white/70 leading-relaxed mb-6">
            From DeFi and NFTs to AI x crypto, we help you validate your ideas, avoid hype traps, and build solutions that solve real problems. Get insight into product-market fit, user flows, and go-to-market strategies tailored to Web3.
          </p>
          <div class="flex items-center text-blue-400 text-sm group-hover:text-blue-300 transition-colors">
            <span>Validate Web3 products</span>
            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
          </div>
        </div>

        <!-- NFT Marketplace & Tokenization -->
        <div class="glass-effect bg-gradient-to-br from-white/10 to-white/5 border border-white/10 rounded-2xl p-8 group hover:border-purple-500/30 transition-all duration-300 floating-animation" style="animation-delay: -4s;">
          <div class="flex items-start space-x-4 mb-6">
            <div class="feature-icon bg-purple-600/20 border-purple-500/30">
              <i class="fas fa-gem text-purple-400"></i>
            </div>
            <div>
              <h3 class="text-2xl text-white mb-2 title-bold">NFT Marketplace & Tokenization</h3>
              <div class="text-purple-400 text-sm font-medium mb-3">Digital Assets</div>
            </div>
          </div>
          <p class="text-white/70 leading-relaxed mb-6">
            Launch full-featured NFT marketplaces with advanced trading features, royalty systems, and cross-chain compatibility. Transform physical assets into tradeable digital tokens.
          </p>
          <div class="flex items-center text-purple-400 text-sm group-hover:text-purple-300 transition-colors">
            <span>Create NFT solutions</span>
            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
          </div>
        </div>

        <!--  Blockchain Integration -->
        <div class="glass-effect bg-gradient-to-br from-white/10 to-white/5 border border-white/10 rounded-2xl p-8 group hover:border-emerald-500/30 transition-all duration-300 floating-animation" style="animation-delay: -6s;">
          <div class="flex items-start space-x-4 mb-6">
            <div class="feature-icon bg-emerald-600/20 border-emerald-500/30">
              <i class="fas fa-network-wired text-emerald-400"></i>
            </div>
            <div>
              <h3 class="text-2xl text-white mb-2 title-bold">Blockchain Infrastructure Consulting</h3>
              <div class="text-emerald-400 text-sm font-medium mb-3">Choose the Right Stack, Avoid the Noise</div>
            </div>
          </div>
          <p class="text-white/70 leading-relaxed mb-6">
            Solana, Ethereum, Bitcoin, L2s, Cosmos SDK—we help you choose and configure the right foundation for your product. Whether you’re building a new protocol or improving performance, we bring clarity and hands-on support.


          </p>
          <div class="flex items-center text-emerald-400 text-sm group-hover:text-emerald-300 transition-colors">
            <span>Optimize infrastructure</span>
            <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
          </div>
        </div>
      </div>

      <!-- Technology Stack -->
      <div class="mb-16">
        <h3 class="text-3xl md:text-4xl title-bold text-white mb-8 text-center">
          Cutting-Edge Technology Stack
        </h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div class="glass-effect bg-white/5 border border-white/10 rounded-xl p-6 text-center group hover:border-orange-500/30 transition-all">
            <i class="fab fa-bitcoin text-4xl text-orange-400 mb-3 group-hover:text-orange-300 transition-colors"></i>
            <div class="text-white font-medium">Bitcoin</div>
            <div class="text-white/60 text-sm">Layer 1 & 2, Lightning and Meta Protocols</div>
          </div>
          <div class="glass-effect bg-white/5 border border-white/10 rounded-xl p-6 text-center group hover:border-orange-500/30 transition-all">
            <i class="fab fa-ethereum text-4xl text-blue-400 mb-3 group-hover:text-blue-300 transition-colors"></i>
            <div class="text-white font-medium">Ethereum</div>
            <div class="text-white/60 text-sm">Layer 1 & 2</div>
          </div>
          <div class="glass-effect bg-white/5 border border-white/10 rounded-xl p-6 text-center group hover:border-orange-500/30 transition-all">
            <i class="fas fa-scroll text-4xl text-amber-400 mb-3 group-hover:text-amber-300 transition-colors"></i>
            <div class="text-white font-medium">Ordinals</div>
            <div class="text-white/60 text-sm">Bitcoin NFTs</div>
          </div>
          <div class="glass-effect bg-white/5 border border-white/10 rounded-xl p-6 text-center group hover:border-orange-500/30 transition-all">
            <i class="fas fa-robot text-4xl text-purple-400 mb-3 group-hover:text-purple-300 transition-colors"></i>
            <div class="text-white font-medium">Crypto AI Agents</div>
            <div class="text-white/60 text-sm">Automated Trading</div>
          </div>
          <div class="glass-effect bg-white/5 border border-white/10 rounded-xl p-6 text-center group hover:border-orange-500/30 transition-all">
            <i class="fas fa-cube text-4xl text-purple-400 mb-3 group-hover:text-purple-300 transition-colors"></i>
            <div class="text-white font-medium">Solidity</div>
            <div class="text-white/60 text-sm">Smart Contracts</div>
          </div>
          <div class="glass-effect bg-white/5 border border-white/10 rounded-xl p-6 text-center group hover:border-orange-500/30 transition-all">
            <i class="fas fa-network-wired text-4xl text-emerald-400 mb-3 group-hover:text-emerald-300 transition-colors"></i>
            <div class="text-white font-medium">Polygon</div>
            <div class="text-white/60 text-sm">Scaling Solution</div>
          </div>
          <div class="glass-effect bg-white/5 border border-white/10 rounded-xl p-6 text-center group hover:border-orange-500/30 transition-all">
            <i class="fas fa-shield-alt text-4xl text-amber-400 mb-3 group-hover:text-amber-300 transition-colors"></i>
            <div class="text-white font-medium">Security</div>
            <div class="text-white/60 text-sm">Audited Code</div>
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="text-center">
        <h3 class="text-3xl md:text-4xl title-bold text-white mb-6">
          Ready to Build the Future?
        </h3>
        <p class="text-lg text-white/70 max-w-2xl mx-auto mb-8">
          Join the blockchain revolution. Schedule a free consultation and discover how our blockchain solutions can transform your business and unlock new revenue opportunities.
        </p>
        <button class="px-8 py-4 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-500 hover:to-red-500 text-white font-medium rounded-xl transition-all duration-200 pulse-glow">
          Get Free Blockchain Assessment
        </button>
      </div>
    </div>
  </section>

  <!-- Footer Section -->
  <footer class="pt-16 pb-8 px-6 md:px-12 bg-zinc-900/80 border-t border-zinc-800 backdrop-blur-xl">
    <div class="max-w-7xl mx-auto flex flex-col md:flex-row md:items-center md:justify-between mb-10">
      <div class="mb-8 md:mb-0">
        <div class="text-2xl font-semibold tracking-tight mb-2">
          Disrupt the Block<span class="text-blue-500">.</span>
        </div>
        <p class="text-gray-400 text-[15px] max-w-sm">
          Transforming businesses through innovative technology solutions. Blockchain, AI, and custom software that drives results.
        </p>
      </div>
      <nav class="flex flex-wrap gap-x-8 gap-y-2 items-center text-[15px]">
        <a href="index.html" class="hover:text-blue-400 transition-colors">Home</a>
        <a href="#" class="hover:text-blue-400 transition-colors">Services</a>
        <a href="#" class="hover:text-blue-400 transition-colors">Why Choose Us</a>
        <a href="#" class="hover:text-blue-400 transition-colors">Contact</a>
        <a href="#" class="hover:text-blue-400 transition-colors">Blog</a>
      </nav>
    </div>
    <div class="border-t border-zinc-800 pt-8 flex flex-col md:flex-row md:items-center md:justify-between">
      <div class="text-gray-500 text-[14px] mb-4 md:mb-0">
        &copy; 2024 Disrupt the Block. All rights reserved.
      </div>
      <div class="flex space-x-4">
        <a href="#" class="text-gray-400 hover:text-blue-500 transition-colors" aria-label="LinkedIn">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
          </svg>
        </a>
        <a href="#" class="text-gray-400 hover:text-blue-500 transition-colors" aria-label="Twitter">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
          </svg>
        </a>
        <a href="#" class="text-gray-400 hover:text-blue-500 transition-colors" aria-label="GitHub">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 *********** 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
          </svg>
        </a>
      </div>
    </div>
  </footer>

  <script>
    // Mobile Menu Toggle
    const menuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (menuButton && mobileMenu) {
      const icon = menuButton.querySelector('i');

      const toggleMenu = () => {
        mobileMenu.classList.toggle('hidden');
        if (icon) {
          icon.classList.toggle('fa-bars');
          icon.classList.toggle('fa-times');
        }
      };

      menuButton.addEventListener('click', toggleMenu);

      // Close menu when a link inside is clicked
      mobileMenu.querySelectorAll('a, button').forEach(el => {
        el.addEventListener('click', () => {
          if (!mobileMenu.classList.contains('hidden')) {
            toggleMenu();
          }
        });
      });
    }

    // Initialize wave background
    document.addEventListener('DOMContentLoaded', function() {
      VANTA.WAVES({
        el: "#vanta-bg",
        mouseControls: true,
        touchControls: true,
        gyroControls: false,
        minHeight: 200.00,
        minWidth: 200.00,
        scale: 1.00,
        scaleMobile: 1.00,
        color: 0x1a1a2e,
        shininess: 30.00,
        waveHeight: 20.00,
        waveSpeed: 0.50,
        zoom: 0.75
      });
    });

    // Smooth scroll arrow functionality
    const scrollArrow = document.getElementById('scroll-arrow');
    const blockchainSolutionsSection = document.getElementById('blockchain-solutions');

    if (scrollArrow && blockchainSolutionsSection) {
      scrollArrow.addEventListener('click', () => {
        blockchainSolutionsSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      });
    }
  </script>
</body>
</html> 